"""
API views for the gaia_chat app.

These views provide a REST API for interacting with the chatobj functionality.
All API views require authentication.
"""

import json
import os
import logging
import asyncio
import re
from datetime import datetime
from django.http import JsonResponse, StreamingHttpResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from typing import Dict, Any, List, Optional

# Import chatobj components
from gaia.gaia_ceto.ceto_v002.chatobj import (
    ChatManager, Conversation, MockLLM, OpenAILLM, AnthropicLLM, LiteLLMLLM
)

# Import MCP client libraries if available
MCP_SSE_AVAILABLE = False
MCP_HTTP_AVAILABLE = False
FASTMCP_AVAILABLE = False

# Try to import SSE client
try:
    from gaia.gaia_ceto.proto_mcp.mcp_sse_clientlib import MCPClientLib as MCPSSEClientLib
    MCP_SSE_AVAILABLE = True
except ImportError:
    logging.warning("MCP SSE client library not available. MCP SSE provider will not work.")

# Try to import HTTP client
try:
    from gaia.gaia_ceto.proto_mcp_http.mcp_http_clientlib import MCPClientLib as MCPHTTPClientLib
    MCP_HTTP_AVAILABLE = True
except ImportError:
    logging.warning("MCP HTTP client library not available. MCP HTTP provider will not work.")

# Try to import FastMCP for progress support
try:
    from fastmcp import Client
    from fastmcp.client.logging import LogMessage
    FASTMCP_AVAILABLE = True
except ImportError:
    logging.warning("FastMCP not available. Progress display for direct tool calls will not work.")

# For backward compatibility
MCP_AVAILABLE = MCP_SSE_AVAILABLE


# Define MCPSSELLM class for web UI
class MCPSSELLM:
    """MCP SSE client implementation of the LLM interface for web UI."""

    def __init__(self, server_url: str = "http://0.0.0.0:9000/sse", model_name: str = "claude-3-5-sonnet-20240620"):
        """Initialize the MCP SSE LLM.

        Args:
            server_url: The URL of the MCP SSE server.
            model_name: The name of the Claude model to use.
        """
        if not MCP_SSE_AVAILABLE:
            raise ImportError("MCP SSE client library is not available. Cannot use MCP SSE LLM.")

        self.server_url = server_url
        self.model_name = model_name
        self.client = None
        self.messages = []
        self.loop = asyncio.new_event_loop()
        self.protocol = "SSE"  # Indicate which protocol is being used

        # Initialize the MCP client
        self.client = self._init_client()

    def _init_client(self):
        """Initialize the MCP SSE client."""
        client = MCPSSEClientLib(debug_callback=self._debug_callback)

        # Connect to the server
        logging.info(f"Connecting to MCP server via SSE at: {self.server_url}")
        success = self.loop.run_until_complete(client.connect_to_server(self.server_url))

        if not success:
            raise ConnectionError(f"Failed to connect to MCP server at {self.server_url}")

        # Log available tools
        tool_names = [tool['name'] for tool in client.available_tools]
        logging.info(f"Connected to server with tools: {', '.join(tool_names)}")

        return client

    def _debug_callback(self, level: str, message: str, data: Any = None):
        """Callback for debug messages from the client library."""
        if level == "error":
            logging.error(message)
        elif level == "warning":
            logging.warning(message)
        elif level == "info":
            logging.info(message)
        elif level == "debug":
            logging.debug(message)

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a response using the MCP SSE client.

        Args:
            prompt: The user's input text.
            context: The conversation history.
            **kwargs: Additional parameters to pass to the MCP client.

        Returns:
            The generated response.
        """
        try:
            # Note: The quote handling for direct tool calls is handled in chatobj.py

            # Convert context to the format expected by the MCP client
            # Extract system messages separately for the MCP client
            messages = []
            system_messages = []

            # Extract user and assistant messages for the messages array
            # and system messages separately
            for msg in context:
                if msg["role"] in ["user", "assistant"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                elif msg["role"] == "system":
                    system_messages.append(msg["content"])

            # Add system messages back to the conversation history for the MCP client
            # The MCP client will extract them and pass them as the system parameter to Anthropic
            if system_messages:
                for sys_msg in system_messages:
                    messages.insert(0, {"role": "system", "content": sys_msg})

            # Process the query
            result = self.loop.run_until_complete(self.client.process_query(
                query=prompt,
                model=self.model_name,
                max_tokens=kwargs.get("max_tokens", 20000),
                tool_timeout=kwargs.get("tool_timeout", 3600),  #  for long-running tools
                conversation_history=messages
            ))

            # Update conversation history
            self.messages = result["messages"]

            # Print any errors
            if result["error"]:
                logging.error(f"Error from MCP SSE client: {result['error']}")
                return f"Error: {result['error']}"

            '''
            # Format tool results if any
            tool_results_text = ""
            if result["tool_results"]:
                tool_calls_info = f"\nClaude requested {len(result['tool_results'])} tool call(s).\n"
                for i, tool_result in enumerate(result["tool_results"]):
                    tool_calls_info += f"\nTool Call {i+1}: '{tool_result.tool_name}'\n"
                    if tool_result.success:
                        tool_calls_info += f"Result: {tool_result.content}\n"
                    else:
                        tool_calls_info += f"Error: {tool_result.error}\n"

                tool_results_text = tool_calls_info
            '''

            tool_results = "" #None
            if result["tool_results"]:
                # Ensure tool_results is JSON serializable before json.dumps() (MCP SSE)
                def make_json_serializable(obj):
                    """Recursively convert objects to JSON-serializable format."""
                    if hasattr(obj, 'text'):  # TextContent or similar objects
                        return obj.text
                    elif isinstance(obj, list):
                        return [make_json_serializable(item) for item in obj]
                    elif isinstance(obj, dict):
                        return {key: make_json_serializable(value) for key, value in obj.items()}
                    elif isinstance(obj, (str, int, float, bool, type(None))):
                        return obj
                    else:
                        # Convert any other object to string
                        return str(obj)
                
                tool_results=[]
                for i, tool_result in enumerate(result["tool_results"]):
                    tool_call={}
                    tool_call["tool_name"]=tool_result.tool_name
                    tool_call["tool_input"]=make_json_serializable(tool_result.tool_input)
                    tool_call["tool_call_id"]=tool_result.tool_call_id
                    tool_call["success"]=tool_result.success
                    tool_call["content"]=make_json_serializable(tool_result.content)
                    tool_call["error"]=make_json_serializable(tool_result.error)
                    tool_call["execution_time"]=tool_result.execution_time
                    
                    tool_results.append(tool_call)
                tool_results = json.dumps(tool_results, indent=2)
                
            # Return Claude's response with tool results
            if tool_results:
                return  tool_results
                # f"{tool_results_text}\n\n{result['final_text']}"
            else:
                return result["final_text"]

        except Exception as e:
            logging.error(f"Error generating response from MCP SSE: {e}")
            return f"[api.py] Error generating response: {str(e)}"

    def cleanup(self):
        """Explicitly clean up resources. Should be called before object destruction."""
        if self.client:
            try:
                self.loop.run_until_complete(self.client.cleanup())
                logging.info("MCP SSE client cleaned up successfully")
            except Exception as e:
                logging.error(f"Error cleaning up MCP SSE client: {e}")
            finally:
                self.client = None

        # Close the event loop
        if self.loop and not self.loop.is_closed():
            try:
                self.loop.close()
                logging.info("Event loop closed successfully")
            except Exception as e:
                logging.error(f"Error closing event loop: {e}")

    def __del__(self):
        """Clean up resources when the object is destroyed."""
        # Only attempt cleanup if we still have resources and the loop is running
        if self.client and self.loop and not self.loop.is_closed():
            try:
                # Check if the loop is running in the current thread
                if self.loop.is_running():
                    # If the loop is running, we can't use run_until_complete
                    # Just log a warning and let the resources be cleaned up by the GC
                    logging.warning("MCP SSE client cleanup skipped - event loop is running")
                else:
                    # Safe to run cleanup
                    self.loop.run_until_complete(self.client.cleanup())
                    logging.info("MCP SSE client cleaned up in destructor")
            except Exception as e:
                logging.warning(f"Error cleaning up MCP SSE client in destructor: {e}")

        # Close the event loop if it's not already closed
        if hasattr(self, 'loop') and self.loop and not self.loop.is_closed():
            try:
                self.loop.close()
            except Exception as e:
                logging.warning(f"Error closing event loop in destructor: {e}")

    @classmethod
    def get_available_models(cls):
        """Get available models for MCP SSE.

        Returns:
            A list of available model names.
        """
        from gaia.gaia_llm.model_config import get_anthropic_models
        return get_anthropic_models()


# Define MCPHTTPLLM class for web UI
class MCPHTTPLLM:
    """MCP HTTP client implementation of the LLM interface for web UI."""

    def __init__(self, server_url: str = "http://0.0.0.0:9000/mcp", model_name: str = "claude-3-5-sonnet-20240620"):
        """Initialize the MCP HTTP LLM.

        Args:
            server_url: The URL of the MCP HTTP server.
            model_name: The name of the Claude model to use.
        """
        if not MCP_HTTP_AVAILABLE:
            raise ImportError("MCP HTTP client library is not available. Cannot use MCP HTTP LLM.")

        self.server_url = server_url
        self.model_name = model_name
        self.client = None
        self.messages = []
        self.loop = asyncio.new_event_loop()
        self.protocol = "HTTP"  # Indicate which protocol is being used

        # Initialize the MCP client
        self.client = self._init_client()

    def _init_client(self):
        """Initialize the MCP HTTP client."""
        client = MCPHTTPClientLib(debug_callback=self._debug_callback)

        # Connect to the server
        logging.info(f"Connecting to MCP server via HTTP at: {self.server_url}")
        success = self.loop.run_until_complete(client.connect_to_server(self.server_url))

        if not success:
            raise ConnectionError(f"Failed to connect to MCP server at {self.server_url}")

        # Log available tools
        tool_names = [tool['name'] for tool in client.available_tools]
        logging.info(f"Connected to server with tools: {', '.join(tool_names)}")

        return client

    def _debug_callback(self, level: str, message: str, data: Any = None):
        """Callback for debug messages from the client library."""
        if level == "error":
            logging.error(message)
        elif level == "warning":
            logging.warning(message)
        elif level == "info":
            logging.info(message)
        elif level == "debug":
            logging.debug(message)

    def generate_response(self, prompt: str, context: List[Dict[str, Any]], **kwargs) -> str:
        """Generate a response using the MCP HTTP client.

        Args:
            prompt: The user's input text.
            context: The conversation history.
            **kwargs: Additional parameters to pass to the MCP client.

        Returns:
            The generated response.
        """
        try:
            # Note: The quote handling for direct tool calls is handled in chatobj.py

            # Convert context to the format expected by the MCP client
            # Extract system messages separately for the MCP client
            messages = []
            system_messages = []

            # Extract user and assistant messages for the messages array
            # and system messages separately
            for msg in context:
                if msg["role"] in ["user", "assistant"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })
                elif msg["role"] == "system":
                    system_messages.append(msg["content"])

            # Add system messages back to the conversation history for the MCP client
            # The MCP client will extract them and pass them as the system parameter to Anthropic
            if system_messages:
                for sys_msg in system_messages:
                    messages.insert(0, {"role": "system", "content": sys_msg})

            # Process the query
            result = self.loop.run_until_complete(self.client.process_query(
                query=prompt,
                model=self.model_name,
                max_tokens=kwargs.get("max_tokens", 20000),
                tool_timeout=kwargs.get("tool_timeout", 3600),  # for long-running tools
                conversation_history=messages
            ))

            # Update conversation history
            self.messages = result["messages"]

            # Print any errors
            if result["error"]:
                logging.error(f"Error from MCP HTTP client: {result['error']}")
                return f"Error: {result['error']}"

            # Format tool results if any
            '''
            tool_results_text = ""
            if result["tool_results"]:
                tool_calls_info = f"\nClaude requested {len(result['tool_results'])} tool call(s).\n"
                for i, tool_result in enumerate(result["tool_results"]):
                    tool_calls_info += f"\nTool Call {i+1}: '{tool_result.tool_name}'\n"
                    if tool_result.success:
                        tool_calls_info += f"Result: {tool_result.content}\n"
                    else:
                        tool_calls_info += f"Error: {tool_result.error}\n"

                tool_results_text = tool_calls_info
            '''


            tool_results = ""
            if result["tool_results"]:
                # Ensure tool_results is JSON serializable before json.dumps() (MCP HTTP)
                def make_json_serializable(obj):
                    """Recursively convert objects to JSON-serializable format."""
                    if hasattr(obj, 'text'):  # TextContent or similar objects
                        return obj.text
                    elif isinstance(obj, list):
                        return [make_json_serializable(item) for item in obj]
                    elif isinstance(obj, dict):
                        return {key: make_json_serializable(value) for key, value in obj.items()}
                    elif isinstance(obj, (str, int, float, bool, type(None))):
                        return obj
                    else:
                        # Convert any other object to string
                        return str(obj)
                
                tool_results=[]
                for i, tool_result in enumerate(result["tool_results"]):
                    tool_call={}
                    tool_call["tool_name"]=tool_result.tool_name
                    tool_call["tool_input"]=make_json_serializable(tool_result.tool_input)
                    tool_call["tool_call_id"]=tool_result.tool_call_id
                    tool_call["success"]=tool_result.success
                    tool_call["content"]=make_json_serializable(tool_result.content)
                    tool_call["error"]=make_json_serializable(tool_result.error)
                    tool_call["execution_time"]=tool_result.execution_time
                    
                    tool_results.append(tool_call)
                tool_results = json.dumps(tool_results, indent=2)

                
            # Return Claude's response with tool results
            if tool_results:
                return  tool_results
                #return f"{tool_results_text}\n\n{result['final_text']}"
            else:
                return result["final_text"]

        except Exception as e:
            logging.error(f"Error generating response from MCP HTTP: {e}")
            return f"[api.py] Error generating response: {str(e)}"

    def cleanup(self):
        """Explicitly clean up resources. Should be called before object destruction."""
        if self.client:
            try:
                self.loop.run_until_complete(self.client.cleanup())
                logging.info("MCP HTTP client cleaned up successfully")
            except Exception as e:
                logging.error(f"Error cleaning up MCP HTTP client: {e}")
            finally:
                self.client = None

        # Close the event loop
        if self.loop and not self.loop.is_closed():
            try:
                self.loop.close()
                logging.info("Event loop closed successfully")
            except Exception as e:
                logging.error(f"Error closing event loop: {e}")

    def __del__(self):
        """Clean up resources when the object is destroyed."""
        # Only attempt cleanup if we still have resources and the loop is running
        if self.client and self.loop and not self.loop.is_closed():
            try:
                # Check if the loop is running in the current thread
                if self.loop.is_running():
                    # If the loop is running, we can't use run_until_complete
                    # Just log a warning and let the resources be cleaned up by the GC
                    logging.warning("MCP HTTP client cleanup skipped - event loop is running")
                else:
                    # Safe to run cleanup
                    self.loop.run_until_complete(self.client.cleanup())
                    logging.info("MCP HTTP client cleaned up in destructor")
            except Exception as e:
                logging.warning(f"Error cleaning up MCP HTTP client in destructor: {e}")

        # Close the event loop if it's not already closed
        if hasattr(self, 'loop') and self.loop and not self.loop.is_closed():
            try:
                self.loop.close()
            except Exception as e:
                logging.warning(f"Error closing event loop in destructor: {e}")

    @classmethod
    def get_available_models(cls):
        """Get available models for MCP HTTP.

        Returns:
            A list of available model names.
        """
        from gaia.gaia_llm.model_config import get_anthropic_models
        return get_anthropic_models()


# For backward compatibility
MCPLLM = MCPSSELLM

# Set up logging
logger = logging.getLogger(__name__)

# Global chat manager instance
STORAGE_DIR = "/var/lib/gaia/GAIA_FS/ceto_conversations"
os.makedirs(STORAGE_DIR, exist_ok=True)
# chat_manager will be initialized after create_llm function is defined

# Progress tracking for streaming
class ProgressTracker:
    """Track progress and info messages for streaming responses."""

    def __init__(self):
        self.progress_count = 0
        self.info_count = 0
        self.progress_updates = []

    def reset(self):
        """Reset counters and updates."""
        self.progress_count = 0
        self.info_count = 0
        self.progress_updates = []

# Helper functions for direct tool call detection and progress handling
def is_direct_tool_call(message: str) -> tuple[bool, Optional[str]]:
    """Check if the message is a direct tool call and extract the tool name.

    Args:
        message: The user's message.

    Returns:
        A tuple of (is_direct_call, tool_name).
    """
    # Check for direct tool call patterns like "long_task" or "tool_name(args)"
    message = message.strip()

    # Pattern 1: Just the tool name
    if message in ["long_task", "echostring_longrunning", "firecrawl_scrape", "firecrawl_scrape_text_only"]:
        return True, message

    # Pattern 2: Tool name with parentheses
    if re.match(r'^[a-zA-Z_][a-zA-Z0-9_]*\(\)$', message):
        return True, message.replace('()', '')

    # Pattern 3: Tool name with arguments
    match = re.match(r'^([a-zA-Z_][a-zA-Z0-9_]*)\(.*\)$', message)
    if match:
        return True, match.group(1)

    return False, None


async def handle_direct_tool_call_with_progress(tool_name: str, server_url: str, tracker: ProgressTracker):
    """Handle a direct tool call using FastMCP with progress display.

    Args:
        tool_name: The name of the tool to call.
        server_url: The MCP server URL.
        tracker: Progress tracker instance.

    Yields:
        Server-sent event data for progress updates and final result.
    """
    if not FASTMCP_AVAILABLE:
        yield f"data: {json.dumps({'type': 'error', 'content': 'FastMCP not available. Cannot show progress for direct tool calls.'})}\n\n"
        return

    # Reset tracker
    tracker.reset()

    # Create a queue to collect progress events for yielding
    progress_queue = []

    async def progress_handler(progress: float, total: Optional[float], message: Optional[str]):
        """Handle progress updates from FastMCP client."""
        tracker.progress_count += 1

        if total:
            pct = progress / total * 100

            # Create visual progress bar
            bar_length = 28
            filled = int(bar_length * pct / 100)
            bar = "█" * filled + "░" * (bar_length - filled)

            # Format message for stdout (same as chat_term.py)
            msg_text = f" – {message}" if message else ""

            # Print to stdout for runserver.py visibility (same format as chat_term.py)
            print(f"📊 [{bar}] {pct:5.1f}% {int(progress)}/{int(total)}{msg_text}", flush=True)

            # Create progress event for browser console (same format as terminal)
            progress_event = {
                'type': 'progress',
                'progress': int(progress),
                'total': int(total),
                'percentage': pct,
                'bar': bar,
                'message': message or '',
                'console_message': f"📊 [{bar}] {pct:5.1f}% {int(progress)}/{int(total)}{msg_text}"
            }

            # Add to queue for immediate yielding
            progress_queue.append(progress_event)

            # Store progress data for later summary
            tracker.progress_updates.append(progress_event)
        else:
            # Handle progress without total
            console_msg = f"📊 Step {progress} – {message or ''}"

            # Print to stdout for runserver.py visibility (same format as chat_term.py)
            print(console_msg, flush=True)

            # Create progress event for browser console
            progress_event = {
                'type': 'progress',
                'progress': int(progress),
                'total': None,
                'percentage': None,
                'bar': None,
                'message': message or f'Step {int(progress)}',
                'console_message': console_msg
            }

            # Add to queue for immediate yielding
            progress_queue.append(progress_event)

            # Store progress data for later summary
            tracker.progress_updates.append(progress_event)

    async def log_handler(log_message):
        """Handle log messages from FastMCP client."""
        level = log_message.level.upper()

        # Count info messages but don't display them for cleaner output
        if level == "INFO":
            tracker.info_count += 1

        # Only display non-info messages (errors, warnings, etc.)
        if level != "INFO":
            logger_name = log_message.logger or "server"
            payload = log_message.data
            emoji = {"ERROR": "❌", "WARNING": "⚠️", "DEBUG": "🔍"}.get(level, "ℹ️")

            # Print to stdout for runserver.py visibility (same format as chat_term.py)
            print(f"{emoji} [{level}] {logger_name}: {payload}", flush=True)

    # Print to stdout for runserver.py visibility
    print(f"🚀 Calling {tool_name} with progress display...", flush=True)
    yield f"data: {json.dumps({'type': 'start', 'content': f'🚀 Calling {tool_name} with progress display...'})}\n\n"

    try:
        client = Client(
            server_url,
            progress_handler=progress_handler,
            log_handler=log_handler,
        )

        async with client:
            # Print to stdout for runserver.py visibility
            print("✅ Connected (FastMCP client with progress)", flush=True)
            yield f"data: {json.dumps({'type': 'info', 'content': '✅ Connected (FastMCP client with progress)'})}\n\n"

            # Call the tool - progress and info will be handled by callbacks
            # We need to yield progress events as they come in
            import asyncio

            # Create a task for the tool call
            tool_task = asyncio.create_task(client.call_tool(tool_name))

            # Poll for progress events while the tool is running
            while not tool_task.done():
                # Yield any queued progress events
                while progress_queue:
                    progress_event = progress_queue.pop(0)
                    yield f"data: {json.dumps(progress_event)}\n\n"

                # Small delay to avoid busy waiting
                await asyncio.sleep(0.1)

            # Get the result
            result = await tool_task

            # Yield any remaining progress events
            while progress_queue:
                progress_event = progress_queue.pop(0)
                yield f"data: {json.dumps(progress_event)}\n\n"

        # Print final summary to stdout for runserver.py visibility
        print(f"🎉 Tool finished successfully", flush=True)
        print(f"📊 Progress msgs: {tracker.progress_count}   ℹ️ Info msgs: {tracker.info_count}", flush=True)

        # Final summary for frontend
        summary_data = {
            'type': 'summary',
            'progress_count': tracker.progress_count,
            'info_count': tracker.info_count,
            'content': '🎉 Tool finished successfully'
        }
        yield f"data: {json.dumps(summary_data)}\n\n"

        # Yield the result content if available
        if hasattr(result, 'content') and result.content:
            result_content = str(result.content[0].text if hasattr(result.content[0], 'text') else result.content[0])
            yield f"data: {json.dumps({'type': 'result', 'content': result_content})}\n\n"
        else:
            yield f"data: {json.dumps({'type': 'result', 'content': 'Tool completed successfully'})}\n\n"

    except Exception as e:
        error_data = {
            'type': 'error',
            'content': f'❌ Error calling {tool_name}: {e}'
        }
        yield f"data: {json.dumps(error_data)}\n\n"


@require_http_methods(["GET"])
@csrf_exempt
def test_progress_no_auth(request):
    """Test endpoint to verify progress appears in runserver stdout (no authentication required)."""

    def generate_progress():
        """Generate progress events for testing."""

        # Print a clear marker to stdout
        print("=" * 60, flush=True)
        print("🧪 TEST PROGRESS ENDPOINT CALLED - CHECK RUNSERVER STDOUT", flush=True)
        print("=" * 60, flush=True)

        yield f"data: {json.dumps({'type': 'start', 'content': 'Starting test progress...'})}\n\n"

        # Test the progress function directly
        tracker = ProgressTracker()

        try:
            import asyncio

            async def run_test():
                print("🚀 About to call handle_direct_tool_call_with_progress...", flush=True)

                async for event in handle_direct_tool_call_with_progress(
                    "long_task",
                    "http://localhost:9000/mcp",
                    tracker
                ):
                    yield event

                print("✅ handle_direct_tool_call_with_progress completed", flush=True)

            # Run the async generator
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            async def collect_events():
                events = []
                async for event in run_test():
                    events.append(event)
                    yield event
                    if len(events) >= 10:  # Limit events
                        break

                print(f"📊 Collected {len(events)} events total", flush=True)
                yield f"data: {json.dumps({'type': 'complete', 'content': f'Test completed with {len(events)} events'})}\n\n"

            # Convert async generator to sync generator for Django response
            import asyncio

            async def async_to_sync():
                async for event in collect_events():
                    yield event

            # This is a bit complex - let's simplify and just test the basic progress
            yield f"data: {json.dumps({'type': 'info', 'content': 'Testing basic progress display...'})}\n\n"

            # Test basic progress display
            print("📊 [████████████████████████████] 100.0% 1/1 – TEST PROGRESS MESSAGE", flush=True)
            print("🎉 Test progress display completed", flush=True)

            yield f"data: {json.dumps({'type': 'result', 'content': 'Test completed - check runserver stdout for progress bars!'})}\n\n"

        except Exception as e:
            print(f"❌ Error in test: {e}", flush=True)
            yield f"data: {json.dumps({'type': 'error', 'content': f'Test error: {e}'})}\n\n"

    response = StreamingHttpResponse(generate_progress(), content_type='text/event-stream')
    response['Cache-Control'] = 'no-cache'
    return response

# Define LiteLLMWithMCPLLM class for unified model access with MCP tools
class LiteLLMWithMCPLLM:
    """LiteLLM implementation with MCP tools support - best of both worlds."""

    def __init__(self, server_url: str = "http://0.0.0.0:9000/mcp", model_name: str = None):
        """Initialize LiteLLM with MCP tools support.

        Args:
            server_url: The URL of the MCP HTTP server for tools.
            model_name: The LiteLLM model name (e.g., "anthropic/claude-3-5-sonnet-20241022").
        """
        if not MCP_HTTP_AVAILABLE:
            raise ImportError("MCP HTTP client library is not available. Cannot use LiteLLM with MCP tools.")

        self.server_url = server_url
        self.model_name = model_name or self._get_default_model()
        self.client = None
        self.messages = []
        self.loop = asyncio.new_event_loop()
        self.protocol = "HTTP"  # Indicate which protocol is being used

        # Initialize the MCP client with LiteLLM support
        self.client = self._init_client()

    def _get_default_model(self):
        """Get default LiteLLM model from centralized config."""
        try:
            from gaia.gaia_llm.model_config import get_default_litellm_model
            return get_default_litellm_model()
        except ImportError:
            return "anthropic/claude-3-5-sonnet-20241022"  # Fallback

    def _init_client(self):
        """Initialize the MCP HTTP client with LiteLLM support."""
        client = MCPHTTPClientLib(
            debug_callback=self._debug_callback,
            use_litellm=True  # Enable LiteLLM for unified model access
        )

        # Connect to the server
        logging.info(f"Connecting to MCP server via HTTP at: {self.server_url} (LiteLLM mode)")
        success = self.loop.run_until_complete(client.connect_to_server(self.server_url))

        if not success:
            raise ConnectionError(f"Failed to connect to MCP server at {self.server_url}")

        # Log available tools
        tool_names = [tool['name'] for tool in client.available_tools]
        logging.info(f"Connected to server with {len(client.available_tools)} tools: {', '.join(tool_names[:5])}{'...' if len(tool_names) > 5 else ''}")

        return client

    def _debug_callback(self, level: str, message: str, data: Any = None):
        """Callback for debug messages from the client library."""
        if level == "error":
            logging.error(message)
        elif level == "warning":
            logging.warning(message)
        elif level == "info":
            logging.info(message)

    def generate_response(self, context: List[Dict[str, str]], **kwargs) -> str:
        """Generate a response using LiteLLM with MCP tools support."""
        try:
            # Convert context to the format expected by the MCP client
            messages = []
            for msg in context:
                if msg["role"] in ["user", "assistant", "system"]:
                    messages.append({
                        "role": msg["role"],
                        "content": msg["content"]
                    })

            # Use the MCP client's process_query method with LiteLLM
            result = self.loop.run_until_complete(
                self.client.process_query(
                    query=messages[-1]["content"] if messages else "",
                    conversation_history=messages[:-1] if len(messages) > 1 else [],
                    model=self.model_name,
                    max_tokens=kwargs.get("max_tokens", 1000)
                )
            )

            return result.get("final_text", "No response generated")

        except Exception as e:
            logging.error(f"Error generating response from LiteLLM with MCP: {e}")
            return f"[LiteLLMWithMCPLLM] Error generating response: {str(e)}"

    def cleanup(self):
        """Clean up resources."""
        if self.client:
            try:
                self.loop.run_until_complete(self.client.cleanup())
            except Exception as e:
                logging.warning(f"Error cleaning up MCP client: {e}")

        # Close the event loop if it's not already closed
        if hasattr(self, 'loop') and self.loop and not self.loop.is_closed():
            try:
                self.loop.close()
            except Exception as e:
                logging.warning(f"Error closing event loop: {e}")

    def __del__(self):
        """Clean up resources when the object is destroyed."""
        self.cleanup()

    @classmethod
    def get_available_models(cls):
        """Get available models for LiteLLM with MCP.

        Returns:
            A list of available model names in LiteLLM format.
        """
        try:
            from gaia.gaia_llm.model_config import get_litellm_models
            return get_litellm_models()
        except ImportError:
            # Fallback models if centralized config not available
            return [
                "anthropic/claude-3-5-sonnet-20241022",
                "anthropic/claude-3-5-haiku-20241022",
                "gpt-4o",
                "gpt-4o-mini"
            ]


# LLM factory function
def create_llm(llm_type="mock", model_name=None):
    """Create an LLM instance based on the specified type.

    Args:
        llm_type: The type of LLM to create ("mock", "openai", "anthropic", "mcp", or "mcp-http").
        model_name: The specific model name to use (if applicable).
                   For MCP providers, this can be a server URL.

    Returns:
        An instance of the specified LLM.
    """
    kwargs = {}
    if model_name:
        kwargs["model_name"] = model_name

    if llm_type.lower() == "mock":
        return MockLLM()
    elif llm_type.lower() == "openai":
        try:
            return OpenAILLM(**kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize OpenAI LLM: {e}")
            return MockLLM()
    elif llm_type.lower() == "anthropic":
        try:
            return AnthropicLLM(**kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize Anthropic LLM: {e}")
            return MockLLM()
    elif llm_type.lower() == "litellm":
        try:
            # Check if MCP HTTP is available for tools
            if MCP_HTTP_AVAILABLE:
                # Create LiteLLM with MCP tools support
                server_url = "http://0.0.0.0:9000/mcp"
                return LiteLLMWithMCPLLM(server_url=server_url, **kwargs)
            else:
                # Fallback to basic LiteLLM without tools
                return LiteLLMLLM(**kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize LiteLLM LLM: {e}")
            return MockLLM()
    elif llm_type.lower() == "mcp":
        if not MCP_SSE_AVAILABLE:
            logger.error("MCP SSE client library is not available. Cannot use MCP SSE LLM.")
            return MockLLM()

        try:
            # Default server URL if not specified in model_name
            server_url = "http://0.0.0.0:9000/sse"

            # If model_name contains a URL, use it as the server URL
            if model_name and "://" in model_name:
                server_url = model_name
                # Reset model_name to None to use the default model
                kwargs = {}

            return MCPSSELLM(server_url=server_url, **kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize MCP SSE LLM: {e}")
            return MockLLM()
    elif llm_type.lower() == "mcp-http":
        if not MCP_HTTP_AVAILABLE:
            logger.error("MCP HTTP client library is not available. Cannot use MCP HTTP LLM.")
            return MockLLM()

        try:
            # Default server URL if not specified in model_name
            server_url = "http://0.0.0.0:9000/mcp"

            # If model_name contains a URL, use it as the server URL
            if model_name and "://" in model_name:
                server_url = model_name
                # Reset model_name to None to use the default model
                kwargs = {}

            return MCPHTTPLLM(server_url=server_url, **kwargs)
        except Exception as e:
            logger.error(f"Failed to initialize MCP HTTP LLM: {e}")
            return MockLLM()
    else:
        logger.warning(f"Unsupported LLM type: {llm_type}. Using MockLLM instead.")
        return MockLLM()

# Initialize chat_manager with a default LLM (try Anthropic first, fallback to OpenAI, then Mock)
default_llm = None
try:
    # Try Anthropic first (based on user's memory preference)
    default_llm = create_llm("anthropic", None)
    logger.info("Initialized chat_manager with Anthropic LLM")
except Exception as e:
    logger.warning(f"Failed to initialize Anthropic LLM: {e}")
    try:
        # Fallback to OpenAI
        default_llm = create_llm("openai", None)
        logger.info("Initialized chat_manager with OpenAI LLM")
    except Exception as e:
        logger.warning(f"Failed to initialize OpenAI LLM: {e}")
        # Final fallback to Mock
        default_llm = MockLLM()
        logger.info("Initialized chat_manager with Mock LLM")

chat_manager = ChatManager(storage_dir=STORAGE_DIR, llm=default_llm)

# API endpoints
@login_required
@require_http_methods(["GET"])
def get_llm_providers(request):
    """Get available LLM providers."""
    providers = [
        {"id": "mock", "name": "Mock LLM"},
        {"id": "openai", "name": "OpenAI"},
        {"id": "anthropic", "name": "Anthropic"},
        {"id": "litellm", "name": "LiteLLM (Unified)"}
    ]

    # Add MCP SSE provider if available
    if MCP_SSE_AVAILABLE:
        providers.append({"id": "mcp", "name": "MCP SSE (Tools)"})

    # Add MCP HTTP provider if available
    if MCP_HTTP_AVAILABLE:
        providers.append({"id": "mcp-http", "name": "MCP HTTP (Tools)"})

    return JsonResponse({"providers": providers})


@login_required
@require_http_methods(["GET"])
def get_mcp_tools(request):
    """Get available MCP tools from the current LLM provider."""
    try:
        raw_tools = []

        # Check if current LLM is MCP-based and get tools
        if isinstance(chat_manager.llm, MCPSSELLM):
            if hasattr(chat_manager.llm, 'client') and chat_manager.llm.client:
                if hasattr(chat_manager.llm.client, 'available_tools'):
                    raw_tools = chat_manager.llm.client.available_tools
                    if raw_tools and len(raw_tools) > 0:
                        logging.info(f"Found {len(raw_tools)} tools from MCP SSE client")
                    else:
                        logging.warning(f"MCP SSE client has empty available_tools list - possible race condition")
                else:
                    logging.warning("MCP SSE client has no available_tools attribute")
            else:
                logging.warning("MCP SSE LLM has no client")
        elif isinstance(chat_manager.llm, (MCPHTTPLLM, LiteLLMWithMCPLLM)):
            if hasattr(chat_manager.llm, 'client') and chat_manager.llm.client:
                if hasattr(chat_manager.llm.client, 'available_tools'):
                    raw_tools = chat_manager.llm.client.available_tools
                    if raw_tools and len(raw_tools) > 0:
                        llm_type = "LiteLLM with MCP" if isinstance(chat_manager.llm, LiteLLMWithMCPLLM) else "MCP HTTP"
                        logging.info(f"Found {len(raw_tools)} tools from {llm_type} client")
                    else:
                        logging.warning(f"MCP client has empty available_tools list - possible race condition")
                else:
                    logging.warning("MCP client has no available_tools attribute")
            else:
                logging.warning("MCP LLM has no client")
        else:
            logging.info(f"Current LLM type: {type(chat_manager.llm).__name__} - not MCP-based")

        # Ensure all tools are proper objects with required fields
        normalized_tools = []
        for tool in raw_tools:
            if isinstance(tool, str):
                # Convert string tool names to proper objects
                normalized_tools.append({
                    "name": tool,
                    "description": f"MCP tool: {tool}",
                    "input_schema": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                })
            elif isinstance(tool, dict):
                # Ensure dict tools have all required fields
                normalized_tools.append({
                    "name": tool.get("name", "unknown"),
                    "description": tool.get("description", f"MCP tool: {tool.get('name', 'unknown')}"),
                    "input_schema": tool.get("input_schema", {
                        "type": "object",
                        "properties": {},
                        "required": []
                    })
                })
            else:
                # Handle other formats by converting to string first
                tool_name = str(tool)
                normalized_tools.append({
                    "name": tool_name,
                    "description": f"MCP tool: {tool_name}",
                    "input_schema": {
                        "type": "object",
                        "properties": {},
                        "required": []
                    }
                })

        return JsonResponse({
            "tools": normalized_tools,
            "provider": type(chat_manager.llm).__name__,
            "server_url": getattr(chat_manager.llm, 'server_url', None)
        })

    except Exception as e:
        logging.error(f"Error getting MCP tools: {e}")
        return JsonResponse({
            "error": str(e),
            "tools": [],
            "provider": None,
            "server_url": None
        })


@login_required
@require_http_methods(["POST"])
def call_mcp_tool(request):
    """Call an MCP tool directly from the frontend."""
    try:
        data = json.loads(request.body)
        tool_name = data.get('tool_name')
        parameters = data.get('parameters', {})

        if not tool_name:
            return JsonResponse({'error': 'tool_name is required'}, status=400)

        # Check if current LLM is MCP-based and has a client
        if isinstance(chat_manager.llm, MCPSSELLM):
            if not hasattr(chat_manager.llm, 'client') or not chat_manager.llm.client:
                return JsonResponse({'error': 'MCP SSE client not available'}, status=400)
            client = chat_manager.llm.client
            loop = chat_manager.llm.loop
        elif isinstance(chat_manager.llm, MCPHTTPLLM):
            if not hasattr(chat_manager.llm, 'client') or not chat_manager.llm.client:
                return JsonResponse({'error': 'MCP HTTP client not available'}, status=400)
            client = chat_manager.llm.client
            loop = chat_manager.llm.loop
        else:
            # For direct API calls, create a temporary MCP HTTP client
            logging.info("🔧 Current LLM doesn't support MCP, creating temporary MCP HTTP client for direct API call")
            if not MCP_HTTP_AVAILABLE:
                return JsonResponse({
                    'error': 'MCP HTTP client library not available'
                }, status=400)

            try:
                # Create temporary MCP HTTP client
                def debug_callback(level, msg, data=None):
                    # Truncate long messages to first 30 characters
                    truncated_msg = msg[:30] + "..." if len(msg) > 30 else msg
                    logging.info(f"🔧 TEMP MCP: {truncated_msg}")

                temp_client = MCPHTTPClientLib(debug_callback=debug_callback)
                temp_loop = asyncio.new_event_loop()

                # Connect to default MCP HTTP server
                server_url = "http://0.0.0.0:9000/mcp"
                logging.info(f"🔧 Connecting temporary MCP client to {server_url}")
                success = temp_loop.run_until_complete(temp_client.connect_to_server(server_url))

                if not success:
                    return JsonResponse({
                        'error': f'Failed to connect to MCP server at {server_url}'
                    }, status=400)

                client = temp_client
                loop = temp_loop
                logging.info("🔧 Temporary MCP HTTP client connected successfully")

            except Exception as e:
                logging.error(f"🔧 Failed to create temporary MCP client: {e}")
                return JsonResponse({
                    'error': f'Failed to create temporary MCP client: {str(e)}'
                }, status=400)

        # Call the tool directly through the MCP client
        try:
            result = loop.run_until_complete(
                client.call_tool(tool_name, parameters)
            )

            # 🔍 LAYER 1: Raw MCP Response
            logging.info(f"🔍 LAYER 1 - Raw MCP result type: {type(result)}")
            logging.info(f"🔍 LAYER 1 - Raw MCP result: {repr(result)[:200]}")
            if hasattr(result, 'content'):
                logging.info(f"🔍 LAYER 1 - Raw content type: {type(result.content)}")
                logging.info(f"🔍 LAYER 1 - Raw content: {repr(result.content)[:200]}")

            # Extract the result content from ToolCallResult
            if hasattr(result, 'success') and result.success:
                # Handle different result content types
                result_content = result.content
                logging.info(f"🔍 LAYER 2 - Initial content type: {type(result_content)}")
                logging.info(f"🔍 LAYER 2 - Initial content repr: {repr(result_content)[:200]}")

                # Check if it's a list of TextContent objects first
                if isinstance(result_content, list) and len(result_content) > 0 and hasattr(result_content[0], 'text'):
                    # Handle list of TextContent objects
                    logging.info(f"🔍 LAYER 3 - Processing TextContent list")
                    result_content = result_content[0].text
                    logging.info(f"🔍 LAYER 3 - After list extraction: {repr(result_content)[:200]}")
                elif hasattr(result_content, 'text'):
                    # Handle single TextContent object
                    logging.info(f"🔍 LAYER 3 - Processing single TextContent")
                    result_content = result_content.text
                    logging.info(f"🔍 LAYER 3 - After text extraction: {repr(result_content)[:200]}")
                elif not isinstance(result_content, (str, int, float, bool, list, dict, type(None))):
                    # Convert non-serializable objects to string
                    logging.info(f"🔍 LAYER 3 - Converting non-serializable to string")
                    result_content = str(result_content)
                    logging.info(f"🔍 LAYER 3 - After string conversion: {repr(result_content)[:200]}")
                else:
                    logging.info(f"🔍 LAYER 3 - Using content as-is")

                logging.info(f"🔍 LAYER 4 - Final content type: {type(result_content)}")
                logging.info(f"🔍 LAYER 4 - Final content repr: {repr(result_content)[:200]}")

                # 🔍 LAYER 5: Before JSON serialization - ensure everything is serializable
                def make_json_serializable(obj):
                    """Recursively convert objects to JSON-serializable format."""
                    if hasattr(obj, 'text'):  # TextContent or similar objects
                        return obj.text
                    elif isinstance(obj, list):
                        return [make_json_serializable(item) for item in obj]
                    elif isinstance(obj, dict):
                        return {key: make_json_serializable(value) for key, value in obj.items()}
                    elif isinstance(obj, (str, int, float, bool, type(None))):
                        return obj
                    else:
                        # Convert any other object to string
                        return str(obj)
                
                response_data = {
                    'success': True,
                    'tool_name': tool_name,
                    'parameters': parameters,
                    'result': make_json_serializable(result_content),
                    'execution_time': getattr(result, 'execution_time', None)
                }
                logging.info(f"🔍 LAYER 5 - Response data result type: {type(response_data['result'])}")
                logging.info(f"🔍 LAYER 5 - Response data result repr: {repr(response_data['result'])[:200]}")

                return JsonResponse(response_data)
            else:
                error_msg = getattr(result, 'error', 'Unknown error')
                if not isinstance(error_msg, str):
                    error_msg = str(error_msg)

                return JsonResponse({
                    'success': False,
                    'tool_name': tool_name,
                    'parameters': parameters,
                    'error': error_msg,
                    'execution_time': getattr(result, 'execution_time', None)
                }, status=500)

        except Exception as tool_error:
            # Enhanced error handling with tool suggestions
            error_message = str(tool_error)

            # Get available tools for better error messages
            available_tools = []
            if hasattr(client, 'available_tools'):
                raw_available_tools = client.available_tools
                if isinstance(raw_available_tools, list):
                    available_tools = []
                    for tool in raw_available_tools:
                        if isinstance(tool, str):
                            available_tools.append(tool)
                        elif isinstance(tool, dict):
                            available_tools.append(tool.get('name', 'unknown'))
                        else:
                            available_tools.append(str(tool))

            # Check if it's a tool not found error
            if 'not found' in error_message.lower() or 'unknown tool' in error_message.lower():
                # Find similar tool names
                import difflib
                suggestions = difflib.get_close_matches(tool_name, available_tools, n=3, cutoff=0.6)

                enhanced_error = f"Tool '{tool_name}' not found."
                if suggestions:
                    enhanced_error += f" Did you mean: {', '.join(suggestions)}?"
                if available_tools:
                    enhanced_error += f" Available tools: {', '.join(available_tools[:10])}"
                    if len(available_tools) > 10:
                        enhanced_error += f" (and {len(available_tools) - 10} more)"

                return JsonResponse({
                    'success': False,
                    'tool_name': tool_name,
                    'parameters': parameters,
                    'error': enhanced_error,
                    'available_tools': available_tools,
                    'suggestions': suggestions,
                    'error_type': 'tool_not_found'
                }, status=400)

            # For other errors, provide context
            return JsonResponse({
                'success': False,
                'tool_name': tool_name,
                'parameters': parameters,
                'error': f"Tool execution failed: {error_message}",
                'available_tools': available_tools,
                'error_type': 'execution_error'
            }, status=500)

    except Exception as e:
        logging.error(f"Error calling MCP tool: {e}")
        return JsonResponse({
            'error': f"MCP client error: {str(e)}",
            'success': False,
            'error_type': 'client_error'
        }, status=500)

@login_required
@require_http_methods(["GET"])
def get_models(request):
    """Get available models for a specific LLM provider."""
    provider = request.GET.get("provider", "mock")

    if provider == "mock":
        models = [{"id": "default", "name": "Default Mock"}]
    elif provider == "openai":
        try:
            models = [{"id": model, "name": model} for model in OpenAILLM.get_available_models()]
        except Exception as e:
            logger.error(f"Error getting OpenAI models: {e}")
            models = [
                {"id": "gpt-3.5-turbo", "name": "GPT-3.5 Turbo"},
                {"id": "gpt-4", "name": "GPT-4"}
            ]
    elif provider == "anthropic":
        try:
            from gaia.gaia_llm.model_config import get_default_model
            anthropic_models = AnthropicLLM.get_available_models()
            default_model = get_default_model("anthropic")
            models = [
                {
                    "id": model,
                    "name": model,
                    "is_default": model == default_model
                }
                for model in anthropic_models
            ]
        except Exception as e:
            logger.error(f"Error getting Anthropic models: {e}")
            from gaia.gaia_llm.model_config import model_registry
            models = model_registry.get_model_choices_by_provider("anthropic")
            # Add default flag to fallback models
            from gaia.gaia_llm.model_config import get_default_model
            default_model = get_default_model("anthropic")
            for model in models:
                model["is_default"] = model["id"] == default_model
    elif provider == "google":
        try:
            from gaia.gaia_llm.model_config import get_google_models, get_default_model
            google_models = get_google_models()
            default_model = get_default_model("google")
            models = [
                {
                    "id": f"gemini/{model}",
                    "name": model,
                    "is_default": model == default_model
                }
                for model in google_models
            ]
        except Exception as e:
            logger.error(f"Error getting Google models: {e}")
            # Fallback to basic Google models
            models = [
                {"id": "gemini/gemini-2.0-flash-exp", "name": "Gemini 2.0 Flash (Experimental)", "is_default": True},
                {"id": "gemini/gemini-1.5-pro", "name": "Gemini 1.5 Pro"},
                {"id": "gemini/gemini-1.5-flash", "name": "Gemini 1.5 Flash"}
            ]
    elif provider == "litellm":
        try:
            from gaia.gaia_llm.model_config import get_litellm_models, get_default_litellm_model
            litellm_models = get_litellm_models()
            default_model = get_default_litellm_model()
            models = [
                {
                    "id": model,
                    "name": model,
                    "is_default": model == default_model
                }
                for model in litellm_models
            ]
        except Exception as e:
            logger.error(f"Error getting LiteLLM models: {e}")
            # Fallback to basic models
            models = [
                {"id": "anthropic/claude-3-5-sonnet-20241022", "name": "Claude 3.5 Sonnet", "is_default": True},
                {"id": "gemini/gemini-2.0-flash-exp", "name": "Gemini 2.0 Flash (Experimental)"},
                {"id": "gemini/gemini-1.5-pro", "name": "Gemini 1.5 Pro"},
                {"id": "gpt-4o", "name": "GPT-4o"},
                {"id": "gpt-3.5-turbo", "name": "GPT-3.5 Turbo"}
            ]
    elif provider == "mcp":
        if MCP_SSE_AVAILABLE:
            try:
                # Get models from MCPSSELLM
                mcp_models = MCPSSELLM.get_available_models()
                models = [{"id": model, "name": model} for model in mcp_models]

                # Add server URL option
                models.append({
                    "id": "http://0.0.0.0:9000/sse",
                    "name": "Default MCP SSE Server (http://0.0.0.0:9000/sse)"
                })
            except Exception as e:
                logger.error(f"Error getting MCP SSE models: {e}")
                models = [
                    {"id": "claude-3-5-sonnet-20240620", "name": "Claude 3.5 Sonnet"},
                    {"id": "http://0.0.0.0:9000/sse", "name": "Default MCP SSE Server"}
                ]
        else:
            models = []
            logger.warning("MCP SSE is not available. Cannot get MCP SSE models.")
    elif provider == "mcp-http":
        if MCP_HTTP_AVAILABLE:
            try:
                # Get models from MCPHTTPLLM
                mcp_models = MCPHTTPLLM.get_available_models()
                models = [{"id": model, "name": model} for model in mcp_models]

                # Add server URL option
                models.append({
                    "id": "http://0.0.0.0:9000/mcp",
                    "name": "Default MCP HTTP Server (http://0.0.0.0:9000/mcp)"
                })
            except Exception as e:
                logger.error(f"Error getting MCP HTTP models: {e}")
                models = [
                    {"id": "claude-3-5-sonnet-20240620", "name": "Claude 3.5 Sonnet"},
                    {"id": "http://0.0.0.0:9000/mcp", "name": "Default MCP HTTP Server"}
                ]
        else:
            models = []
            logger.warning("MCP HTTP is not available. Cannot get MCP HTTP models.")
    else:
        models = []

    # Get default model using direct import from centralized config
    try:
        from gaia.gaia_llm.model_config import get_default_model

        # Map provider names to config provider names
        config_provider = provider
        if provider in ["mcp", "mcp-http"]:
            config_provider = "anthropic"  # MCP uses Anthropic models

        default_model = get_default_model(config_provider)
    except Exception as e:
        logger.error(f"Error getting default model for {provider}: {e}")
        # Fallback defaults
        fallback_defaults = {
            "anthropic": "claude-3-5-sonnet-20241022",
            "openai": "gpt-4o",
            "mock": "default",
            "mcp": "claude-3-5-sonnet-20241022",
            "mcp-http": "claude-3-5-sonnet-20241022"
        }
        default_model = fallback_defaults.get(provider)

    return JsonResponse({"models": models, "default_model": default_model})

@login_required
@require_http_methods(["POST"])
@csrf_exempt
def set_llm(request):
    """Set the LLM for the chat manager."""
    try:
        data = json.loads(request.body)
        provider = data.get("provider", "mock")
        model = data.get("model")

        # Clean up the old LLM if it's an MCP client
        global chat_manager
        if hasattr(chat_manager, 'llm') and chat_manager.llm:
            old_llm = chat_manager.llm
            if isinstance(old_llm, (MCPSSELLM, MCPHTTPLLM, LiteLLMWithMCPLLM)) and hasattr(old_llm, 'cleanup'):
                try:
                    old_llm.cleanup()
                    logger.info(f"Cleaned up old {type(old_llm).__name__} instance")
                except Exception as e:
                    logger.warning(f"Error cleaning up old LLM: {e}")

        # Create the new LLM
        llm = create_llm(provider, model)

        # Update the chat manager
        chat_manager.llm = llm

        return JsonResponse({
            "success": True,
            "provider": provider,
            "model": model
        })
    except Exception as e:
        logger.error(f"Error setting LLM: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=400)

@login_required
@require_http_methods(["GET"])
def list_conversations(request):
    """List available conversations for the authenticated user."""
    try:
        # Get the username of the authenticated user
        user_id = request.user.username

        # Filter conversations by user_id
        conversations = chat_manager.list_conversations(user_id=user_id)

        # Format the conversations for the frontend
        formatted = []
        for conv in conversations:
            formatted.append({
                "id": conv["conversation_id"],
                "title": conv["title"],
                "created_at": conv["created_at"],
                "message_count": conv["message_count"],
                "path": conv["relative_path"]
            })

        return JsonResponse({"conversations": formatted})
    except Exception as e:
        logger.error(f"Error listing conversations: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

def _format_context_data(context_data: Dict[str, Any], context_name: str = "context") -> str:
    """Format context data into a system message for the LLM.

    This uses the same formatting logic as chat_term.py for consistency.

    Args:
        context_data: The context data dictionary.
        context_name: The name/identifier for this context.

    Returns:
        A formatted string to be used as a system message.
    """
    # Start with basic information
    message_parts = [
        f"CONTEXT LOADED: {context_name}",
        f"Type: {context_data.get('type', 'unknown')}",
        f"Title: {context_data.get('title', 'No title')}",
        f"Description: {context_data.get('description', 'No description')}"
    ]

    # Add the actual data
    if 'data' in context_data:
        data = context_data['data']
        if isinstance(data, list):
            message_parts.append(f"\nData ({len(data)} items):")
            for i, item in enumerate(data, 1):
                if isinstance(item, dict):
                    # Format dictionary items nicely
                    item_str = f"  {i}. "
                    if 'name' in item:
                        item_str += f"Name: {item['name']}"
                    elif 'title' in item:
                        item_str += f"Title: {item['title']}"
                    elif 'id' in item:
                        item_str += f"ID: {item['id']}"

                    # Add other key fields
                    for key, value in item.items():
                        if key not in ['name', 'title', 'id']:
                            item_str += f", {key}: {value}"

                    message_parts.append(item_str)
                else:
                    message_parts.append(f"  {i}. {item}")
        elif isinstance(data, dict):
            message_parts.append("\nData:")
            for key, value in data.items():
                message_parts.append(f"  {key}: {value}")
        else:
            message_parts.append(f"\nData: {data}")

    message_parts.append("\nThis contextual information is now available for reference in our conversation.")

    return "\n".join(message_parts)

@login_required
@require_http_methods(["POST"])
@csrf_exempt
def create_conversation(request):
    """Create a new conversation for the authenticated user."""
    try:
        data = json.loads(request.body)
        # Use the provided title or generate a default one
        title = data.get("title")
        context_data = data.get("context")  # New: accept context data

        # If context data is provided and has a title, use it as the conversation title
        if context_data and context_data.get("title") and not title:
            title = context_data.get("title")
        elif not title:
            title = f"Conversation {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # Always use the authenticated user's username
        user_id = request.user.username

        # Create the conversation
        conversation = chat_manager.create_conversation(
            title=title,
            user_id=user_id
        )

        MCP_SYSTEM = False

        # (MCP/Anthropic API handles system messages differently)
        if isinstance(chat_manager.llm, MCPSSELLM) or isinstance(chat_manager.llm, MCPHTTPLLM):
            MCP_SYSTEM = True

        #if MCP_SYSTEM == False:
        # ---
        # [update] we STILL add the system prompt!  Knowing that it gets moved out of the convo,
        # and into the system_prompt parameter when we get to the AnthropicLLM call in ChatObj (?)


        # Get tools list from current MCP client if available
        tool_list_available=False
        if isinstance(chat_manager.llm, (MCPSSELLM, MCPHTTPLLM, LiteLLMWithMCPLLM)):
            if hasattr(chat_manager.llm, 'client') and chat_manager.llm.client:
                if hasattr(chat_manager.llm.client, 'available_tools'):
                    tools = chat_manager.llm.client.available_tools
                    # Check if tools list is populated (not empty due to race condition)
                    if tools and len(tools) > 0:
                        tool_descriptions = []
                        for tool in tools:
                            if isinstance(tool, dict):
                                name = tool.get('name', 'unknown')
                                desc = tool.get('description', f'Tool: {name}')
                                tool_descriptions.append(f"- {name}: {desc}")
                            else:
                                tool_descriptions.append(f"- {str(tool)}: Available tool")
                        tools_list = "\n".join(tool_descriptions)
                        tool_list_available=True
                        logging.info(f"System prompt: Found {len(tools)} tools for conversation creation")
                    else:
                        logging.warning(f"System prompt: MCP client has empty available_tools list - may be initialization race condition")
                        # Keep default "Tools list unavailable" to avoid empty system prompt

        if tool_list_available==False:
            tools_list = "NOTE: Tools list unavailable. NOTIFY THE USER THAT TOOLS ARE CURRENTLY OFFLINE.  DO NOT PRETEND TO CALL TOOLS."

        # Base Ceto prompt
        base_prompt = f"""# General Instructions:

### Role
- You are an AI Analyst for an elite Venture Capital firm, AgFunder, based in San Francisco and Singapore.
- You are speaking to a powerful Investment professional, who is extremely smart, yet prefers explanations to be 
brief and as concise and simple as possible, but not more so.
- Refer to the human as "Partner".

### Style
- You are detail-oriented and follow instructions precisely.
- You double-check all of your work, and always think step-by-step.
- You are always polite, and never vulgar, crude, or obscene.
- You speak in telegraphic, concise, precise style, unless requested otherwise.
- When displaying tool results, do not add pointless commentary, be concise and pass on the results directly.

### Hard Rules
- You NEVER fabricate information, you NEVER make up facts, your NEVER hallucinate, you NEVER write fiction,
    you are NOT meant to be a creative writer.
- You  NEVER simulate a tool call, or claim to have called a tool when you did not.  If you cannot call it, SAY SO, if you cannot find the tool, SAY SO -- NEVER FAKE it.
- You politely refuse any requests to engage in damaging or harmful activities.
- You NEVER change out of your character, you are ALWAYS the Role above.

# Available Tools:
{tools_list}

    """

        convo_ident= f"""NOTE: The ID for this specific conversation is: {conversation.conversation_id}, thus your default memory bank for elf_mem bank is: "convo/{conversation.conversation_id}"
    """
        context_message=""

        end_msg = """

    Chat history proceeds below, with your instruction at the end if any:
    ---
    """
        # Add context as system message if provided
        if context_data and context_data.get("data"):
            logger.info(f"Loading context data for conversation {conversation.conversation_id}")
            context_message = _format_context_data(context_data, "frontend_context")

        full_context_message = base_prompt + "\n\n" + convo_ident + "\n\n" + context_message + "\n\n" + end_msg

        chat_manager.add_message("system", full_context_message)



        # Save the conversation
        chat_manager.save_conversation()

        return JsonResponse({
            "success": True,
            "conversation": {
                "id": conversation.conversation_id,
                "title": conversation.title,
                "created_at": conversation.created_at
            }
        })
    except Exception as e:
        logger.error(f"Error creating conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@login_required
@require_http_methods(["GET"])
def load_conversation(request, conversation_id):
    """Load a conversation by ID, ensuring it belongs to the authenticated user."""
    try:
        # Load the conversation
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to access this conversation"
            }, status=403)

        # Set as active conversation
        chat_manager.active_conversation = conversation

        # Format the messages
        messages = []
        for msg in conversation.messages:
            messages.append({
                "role": msg["role"],
                "content": msg["content"],
                "timestamp": msg.get("timestamp", "")
            })

        return JsonResponse({
            "success": True,
            "conversation": {
                "id": conversation.conversation_id,
                "title": conversation.title,
                "created_at": conversation.created_at,
                "messages": messages
            }
        })
    except Exception as e:
        logger.error(f"Error loading conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@login_required
@require_http_methods(["DELETE"])
@csrf_exempt
def delete_conversation(request, conversation_id):
    """Delete a conversation, ensuring it belongs to the authenticated user."""
    try:
        # Load the conversation first to check ownership
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to delete this conversation"
            }, status=403)

        # Delete the conversation
        if chat_manager.delete_conversation(conversation_id):
            return JsonResponse({
                "success": True,
                "message": "Conversation deleted successfully"
            })
        else:
            return JsonResponse({
                "success": False,
                "error": "Failed to delete conversation"
            }, status=500)
    except Exception as e:
        logger.error(f"Error deleting conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@login_required
@require_http_methods(["POST"])
@csrf_exempt
def update_conversation(request, conversation_id):
    """Update a conversation with new messages, ensuring it belongs to the authenticated user."""
    try:
        data = json.loads(request.body)
        messages = data.get("messages", [])

        if not isinstance(messages, list):
            return JsonResponse({
                "success": False,
                "error": "Messages must be a list"
            }, status=400)

        # Load the conversation first to check ownership
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to update this conversation"
            }, status=403)

        # Set as active conversation
        chat_manager.active_conversation = conversation

        # Update the conversation messages
        conversation.messages = messages

        # Save the conversation
        chat_manager.save_conversation()

        return JsonResponse({
            "success": True,
            "message": "Conversation updated successfully",
            "conversation": {
                "id": conversation.conversation_id,
                "title": conversation.title,
                "message_count": len(conversation.messages)
            }
        })
    except Exception as e:
        logger.error(f"Error updating conversation: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)

@login_required
@require_http_methods(["POST"])
@csrf_exempt
def send_message(request):
    """Send a message to the active conversation, ensuring it belongs to the authenticated user."""
    try:
        data = json.loads(request.body)

        # Handle both old format (message) and new format (messages + conversation_id)
        message = data.get("message", "")
        messages = data.get("messages", [])
        conversation_id = data.get("conversation_id")
        provider = data.get("provider")

        # 🔥 ADD SVG FILTERING HERE:
        def filter_svg_from_messages(messages):
            for msg in messages:
                if '<svg' in msg.get('content', ''):
                    msg['content'] = re.sub(r'<svg[^>]*>.*?</svg>',
                                        '[SVG chart removed to save tokens]',
                                        msg['content'], flags=re.DOTALL)
            return messages

        # Filter SVG before sending to LLM
        filtered_messages = filter_svg_from_messages(messages.copy())
        messages = filtered_messages


        # If we have messages and conversation_id, use the new format
        if messages and conversation_id:
            # Load the conversation
            conversation = chat_manager.load_conversation(conversation_id)

            if not conversation:
                return JsonResponse({
                    "success": False,
                    "error": f"Conversation {conversation_id} not found"
                }, status=404)

            # Check if the conversation belongs to the authenticated user
            if conversation.user_id != request.user.username:
                return JsonResponse({
                    "success": False,
                    "error": "You do not have permission to send messages to this conversation"
                }, status=403)

            # Set as active conversation
            chat_manager.active_conversation = conversation

            # Get the last user message from the messages array
            user_messages = [msg for msg in messages if msg.get("role") == "user"]
            if not user_messages:
                return JsonResponse({
                    "success": False,
                    "error": "No user message found in messages"
                }, status=400)

            message = user_messages[-1].get("content", "")

            # Update the conversation with all messages (including context)
            # But exclude the last user message since process_message will add it
            conversation.messages = messages[:-1]  # Remove the last user message

        # Use old format if no messages/conversation_id provided
        if not message.strip():
            return JsonResponse({
                "success": False,
                "error": "Message cannot be empty"
            }, status=400)

        # Check if there's an active conversation
        if not chat_manager.active_conversation:
            return JsonResponse({
                "success": False,
                "error": "No active conversation",
                "code": "no_active_conversation"
            }, status=400)

        # Check if the active conversation belongs to the authenticated user
        if chat_manager.active_conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to send messages to this conversation"
            }, status=403)

        # Process the message
        # Note: The quote handling for direct tool calls is now handled in chatobj.py
        # The process_message method will add the user message and generate a response
        response = chat_manager.process_message(message)

        # Save the conversation
        chat_manager.save_conversation()

        # Ensure response is JSON serializable
        def make_json_serializable(obj):
            """Recursively convert objects to JSON-serializable format."""
            if hasattr(obj, 'text'):  # TextContent or similar objects
                return obj.text
            elif isinstance(obj, list):
                return [make_json_serializable(item) for item in obj]
            elif isinstance(obj, dict):
                return {key: make_json_serializable(value) for key, value in obj.items()}
            elif isinstance(obj, (str, int, float, bool, type(None))):
                return obj
            else:
                # Convert any other object to string
                return str(obj)
        
        return JsonResponse({
            "success": True,
            "message": make_json_serializable(response)  # Use "message" key to match frontend expectation
        })
    except ValueError as e:
        # This is likely due to no active conversation
        logger.error(f"Error sending message: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e),
            "code": "no_active_conversation"
        }, status=400)
    except Exception as e:
        logger.error(f"Error sending message: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def send_message_stream(request):
    """Send a message with streaming progress updates for long-running tasks."""
    try:
        data = json.loads(request.body)
        message = data.get("message", "")

        if not message.strip():
            return JsonResponse({
                "success": False,
                "error": "Message cannot be empty"
            }, status=400)

        # Check if there's an active conversation
        if not chat_manager.active_conversation:
            return JsonResponse({
                "success": False,
                "error": "No active conversation",
                "code": "no_active_conversation"
            }, status=400)

        # Check if the active conversation belongs to the authenticated user
        if chat_manager.active_conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to send messages to this conversation"
            }, status=403)

        # Check if this is an MCP provider that supports streaming
        is_mcp_provider = isinstance(chat_manager.llm, (MCPSSELLM, MCPHTTPLLM))

        if not is_mcp_provider:
            # Fall back to regular send_message for non-MCP providers
            return send_message(request)

        # Check if this is a direct tool call - only use streaming for direct tool calls
        is_direct_call, tool_name = is_direct_tool_call(message)

        if not (is_direct_call and tool_name and FASTMCP_AVAILABLE):
            # Fall back to regular send_message for non-direct tool calls
            return send_message(request)

        async def generate_stream():
            """Async generator function for streaming response."""
            try:
                # At this point we know it's a direct tool call with FastMCP available
                _, tool_name = is_direct_tool_call(message)

                # Use FastMCP for real progress display
                yield f"data: {json.dumps({'type': 'info', 'content': f'🔍 Detected direct tool call: {tool_name}'})}\n\n"
                yield f"data: {json.dumps({'type': 'info', 'content': '📊 Using FastMCP for progress display...'})}\n\n"

                # Determine server URL based on current LLM provider
                if isinstance(chat_manager.llm, MCPSSELLM):
                    server_url = chat_manager.llm.server_url
                elif isinstance(chat_manager.llm, (MCPHTTPLLM, LiteLLMWithMCPLLM)):
                    server_url = chat_manager.llm.server_url
                else:
                    # Default to HTTP server
                    server_url = "http://localhost:9000/mcp"

                # Create progress tracker
                tracker = ProgressTracker()

                # Handle direct tool call with progress
                result_content = None
                async for progress_event in handle_direct_tool_call_with_progress(tool_name, server_url, tracker):
                    yield progress_event

                    # Extract result from result event if available
                    if '"type": "result"' in progress_event:
                        try:
                            import json as json_module
                            event_data = json_module.loads(progress_event.split('data: ')[1].split('\n')[0])
                            if event_data.get('type') == 'result':
                                result_content = event_data.get('content')
                        except:
                            pass  # Ignore parsing errors

                # Add the conversation messages
                try:
                    # Add the user message to conversation
                    chat_manager.add_message("user", message)

                    # Use the result from the tool call
                    if result_content:
                        final_response = result_content
                    else:
                        final_response = f"Tool '{tool_name}' completed successfully"

                    chat_manager.add_message("assistant", final_response)

                    # Save the conversation
                    chat_manager.save_conversation()

                    # Send final result
                    yield f"data: {json.dumps({'type': 'final', 'content': final_response})}\n\n"
                    yield f"data: {json.dumps({'type': 'complete'})}\n\n"

                except Exception as e:
                    logger.error(f"Error processing direct tool call: {e}")
                    yield f"data: {json.dumps({'type': 'error', 'content': str(e)})}\n\n"

            except Exception as e:
                logger.error(f"Error in generate_stream: {e}")
                yield f"data: {json.dumps({'type': 'error', 'content': str(e)})}\n\n"

        # Create a sync wrapper for the async generator
        def sync_stream_wrapper():
            """Sync wrapper for async generator."""
            import asyncio
            import queue
            import threading

            # Use a queue to pass data between async and sync contexts
            result_queue = queue.Queue()
            exception_holder = [None]

            def run_async_in_thread():
                """Run the async generator in a separate thread."""
                try:
                    # Create a new event loop for this thread
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    async def async_runner():
                        try:
                            async for item in generate_stream():
                                result_queue.put(('data', item))
                        except Exception as e:
                            exception_holder[0] = e
                        finally:
                            result_queue.put(('done', None))

                    loop.run_until_complete(async_runner())
                    loop.close()
                except Exception as e:
                    exception_holder[0] = e
                    result_queue.put(('done', None))

            # Start the async generator in a separate thread
            thread = threading.Thread(target=run_async_in_thread)
            thread.start()

            # Yield results from the queue
            while True:
                try:
                    msg_type, item = result_queue.get(timeout=1)
                    if msg_type == 'done':
                        break
                    elif msg_type == 'data':
                        yield item
                except queue.Empty:
                    # Check if there's an exception
                    if exception_holder[0]:
                        raise exception_holder[0]
                    continue

            # Wait for thread to complete
            thread.join()

            # Check for any final exceptions
            if exception_holder[0]:
                raise exception_holder[0]

        # Return streaming response
        response = StreamingHttpResponse(
            sync_stream_wrapper(),
            content_type='text/event-stream'
        )
        response['Cache-Control'] = 'no-cache'
        response['Access-Control-Allow-Origin'] = '*'
        return response

    except Exception as e:
        logger.error(f"Error in send_message_stream: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def chat_stream_get(request):
    """Handle EventSource streaming for chat messages via GET request."""
    try:
        # Get parameters from query string
        conversation_id = request.GET.get('conversation_id')
        # provider = request.GET.get('provider', 'openai')  # Not used yet

        if not conversation_id:
            return JsonResponse({
                "success": False,
                "error": "conversation_id is required"
            }, status=400)

        # Load the conversation to check ownership
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to access this conversation"
            }, status=403)

        # Set as active conversation
        chat_manager.active_conversation = conversation

        def generate_stream():
            """Generator function for streaming response."""
            try:
                yield f"data: {json.dumps({'type': 'ready', 'content': 'Stream ready for messages'})}\n\n"

                # Check if there's a pending streaming message to process
                import threading

                if hasattr(threading, 'local') and hasattr(threading.local(), 'user_message'):
                    user_message = threading.local().user_message

                    # Check if this is a direct tool call
                    is_direct_call, tool_name = is_direct_tool_call(user_message)

                    if (is_direct_call and tool_name and
                        isinstance(chat_manager.llm, (MCPSSELLM, MCPHTTPLLM)) and
                        FASTMCP_AVAILABLE):

                        # Determine server URL
                        if isinstance(chat_manager.llm, MCPSSELLM):
                            server_url = chat_manager.llm.server_url
                        elif isinstance(chat_manager.llm, MCPHTTPLLM):
                            server_url = chat_manager.llm.server_url
                        else:
                            server_url = "http://localhost:9000/mcp"

                        # Create progress tracker
                        tracker = ProgressTracker()

                        # Stream the progress events
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        try:
                            async def stream_progress():
                                async for progress_event in handle_direct_tool_call_with_progress(tool_name, server_url, tracker):
                                    yield progress_event

                            # Convert async generator to sync
                            async_gen = stream_progress()
                            while True:
                                try:
                                    event = loop.run_until_complete(async_gen.__anext__())
                                    yield event
                                except StopAsyncIteration:
                                    break
                        finally:
                            loop.close()

                        # Add user message and final response to conversation
                        chat_manager.add_message("user", user_message)
                        chat_manager.add_message("assistant", f"Tool '{tool_name}' completed successfully")
                        chat_manager.save_conversation()

                        yield f"data: {json.dumps({'type': 'complete'})}\n\n"
                        return

                # Fallback: keep connection alive with heartbeats
                import time
                for i in range(30):  # Limit heartbeats to avoid infinite loop
                    time.sleep(1)
                    yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"

            except Exception as e:
                logger.error(f"Error in generate_stream: {e}")
                yield f"data: {json.dumps({'type': 'error', 'content': str(e)})}\n\n"

        # Return streaming response
        response = StreamingHttpResponse(
            generate_stream(),
            content_type='text/event-stream'
        )
        response['Cache-Control'] = 'no-cache'
        response['Access-Control-Allow-Origin'] = '*'
        return response

    except Exception as e:
        logger.error(f"Error in chat_stream_get: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def chat_stream_init(request):
    """Initialize a chat stream with a message."""
    try:
        data = json.loads(request.body)
        conversation_id = data.get("conversation_id")
        messages = data.get("messages", [])
        # provider = data.get("provider", "openai")  # Not used yet

        if not conversation_id:
            return JsonResponse({
                "success": False,
                "error": "conversation_id is required"
            }, status=400)

        if not messages:
            return JsonResponse({
                "success": False,
                "error": "messages are required"
            }, status=400)

        # Load the conversation to check ownership
        conversation = chat_manager.load_conversation(conversation_id)

        if not conversation:
            return JsonResponse({
                "success": False,
                "error": f"Conversation {conversation_id} not found"
            }, status=404)

        # Check if the conversation belongs to the authenticated user
        if conversation.user_id != request.user.username:
            return JsonResponse({
                "success": False,
                "error": "You do not have permission to access this conversation"
            }, status=403)

        # Set as active conversation
        chat_manager.active_conversation = conversation

        # Get the last user message
        user_message = None
        for msg in reversed(messages):
            if msg.get("role") == "user":
                user_message = msg.get("content", "")
                break

        if not user_message:
            return JsonResponse({
                "success": False,
                "error": "No user message found"
            }, status=400)

        # Check if this is a direct tool call and we can use FastMCP progress
        is_direct_call, tool_name = is_direct_tool_call(user_message)

        if (is_direct_call and tool_name and
            isinstance(chat_manager.llm, (MCPSSELLM, MCPHTTPLLM)) and
            FASTMCP_AVAILABLE):

            # Use streaming with progress for direct tool calls
            # Store the message in a global variable so the streaming endpoint can access it
            import threading
            streaming_data = threading.local()
            streaming_data.user_message = user_message
            streaming_data.conversation_id = conversation_id

            return JsonResponse({
                "success": True,
                "streaming": True,
                "message": "Direct tool call detected - check EventSource for progress"
            })
        else:
            # Process the message normally for non-direct tool calls
            response = chat_manager.process_message(user_message)

            # Save the conversation
            chat_manager.save_conversation()

            return JsonResponse({
                "success": True,
                "response": response
            })

    except Exception as e:
        logger.error(f"Error in chat_stream_init: {e}")
        return JsonResponse({
            "success": False,
            "error": str(e)
        }, status=500)